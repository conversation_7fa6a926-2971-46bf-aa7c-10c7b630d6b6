<html lang="en" style=""><head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>MyBuilder - Build Beautiful Websites Without Code</title>
	<style>
		* {
			margin: 0;
			padding: 0;
			box-sizing: border-box;
			font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
		}

		body {
			line-height: 1.6;
			color: #333;
		}

		.container {
			width: 90%;
			max-width: 1200px;
			margin: 0 auto;
		}

		header {
			background-color: #fff;
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
			position: fixed;
			width: 100%;
			top: 0;
			z-index: 1000;
		}

		nav {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20px 0;
		}

		.logo {
			font-size: 24px;
			font-weight: 700;
			color: #4361ee;
		}

		.nav-links {
			display: flex;
			list-style: none;
		}

		.nav-links li {
			margin-left: 30px;
		}

		.nav-links a {
			text-decoration: none;
			color: #333;
			transition: color 0.3s;
		}

		.nav-links a:hover {
			color: #4361ee;
		}

		.btn {
			border: none;
			display: inline-block;
			background-color: #4361ee;
			color: white;
			padding: 10px 24px;
			border-radius: 4px;
			text-decoration: none;
			transition: background-color 0.3s;
		}

		.btn:hover {
			background-color: #3a56d4;
		}

		.hero {
			background: linear-gradient(135deg, #4361ee, #3a0ca3);
			color: white;
			padding: 150px 0 100px;
			text-align: center;
		}

		.hero h1 {
			font-size: 48px;
			margin-bottom: 20px;
		}

		.hero p {
			font-size: 20px;
			max-width: 600px;
			margin: 0 auto 40px;
		}

		.features {
			padding: 100px 0;
			background-color: #f9f9f9;
		}

		.section-title {
			text-align: center;
			margin-bottom: 60px;
		}

		.section-title h2 {
			font-size: 36px;
			color: #333;
		}

		.feature-grid {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
			gap: 40px;
		}

		.feature-card {
			background: white;
			border-radius: 8px;
			padding: 30px;
			box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
			transition: transform 0.3s;
		}

		.feature-card:hover {
			transform: translateY(-5px);
		}

		.feature-card h3 {
			font-size: 22px;
			margin-bottom: 15px;
			color: #4361ee;
		}

		.how-it-works {
			padding: 100px 0;
		}

		.steps {
			display: flex;
			justify-content: space-between;
			max-width: 900px;
			margin: 0 auto;
		}

		.step {
			text-align: center;
			width: 30%;
		}

		.step-number {
			width: 60px;
			height: 60px;
			border-radius: 50%;
			background-color: #4361ee;
			color: white;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 24px;
			margin: 0 auto 20px;
		}

		.testimonials {
			background-color: #f9f9f9;
			padding: 100px 0;
		}

		.testimonial-grid {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
			gap: 40px;
		}

		.testimonial-card {
			background: white;
			border-radius: 8px;
			padding: 30px;
			box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
		}

		.testimonial-text {
			font-style: italic;
			margin-bottom: 20px;
		}

		.testimonial-author {
			font-weight: bold;
		}

		.pricing {
			padding: 100px 0;
		}

		.pricing-grid {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
			gap: 40px;
		}

		.pricing-card {
			background: white;
			border-radius: 8px;
			padding: 40px 30px;
			box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
			text-align: center;
			transition: transform 0.3s;
		}

		.pricing-card:hover {
			transform: scale(1.03);
		}

		.pricing-card.featured {
			border: 2px solid #4361ee;
			position: relative;
		}

		.pricing-tag {
			position: absolute;
			top: -12px;
			left: 50%;
			transform: translateX(-50%);
			background: #4361ee;
			color: white;
			padding: 5px 15px;
			border-radius: 20px;
			font-size: 14px;
		}

		.price {
			font-size: 48px;
			margin: 20px 0;
			color: #333;
		}

		.price span {
			font-size: 18px;
			color: #666;
		}

		.pricing-features {
			list-style: none;
			margin-bottom: 30px;
		}

		.pricing-features li {
			margin-bottom: 10px;
			color: #666;
		}

		.cta {
			background: linear-gradient(135deg, #4361ee, #3a0ca3);
			color: white;
			text-align: center;
			padding: 80px 0;
		}

		.cta h2 {
			font-size: 36px;
			margin-bottom: 20px;
		}

		.cta p {
			max-width: 600px;
			margin: 0 auto 40px;
			font-size: 18px;
		}

		footer {
			background-color: #333;
			color: #fff;
			padding: 60px 0 20px;
		}

		.footer-content {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
			gap: 40px;
			margin-bottom: 40px;
		}

		.footer-column h3 {
			margin-bottom: 20px;
			font-size: 18px;
		}

		.footer-links {
			list-style: none;
		}

		.footer-links li {
			margin-bottom: 10px;
		}

		.footer-links a {
			color: #bbb;
			text-decoration: none;
			transition: color 0.3s;
		}

		.footer-links a:hover {
			color: white;
		}

		.copyright {
			text-align: center;
			padding-top: 20px;
			border-top: 1px solid #444;
			color: #bbb;
			font-size: 14px;
		}

		@media (max-width: 768px) {
			.nav-links {
				display: none;
			}

			.hero h1 {
				font-size: 36px;
			}

			.steps {
				flex-direction: column;
				align-items: center;
			}

			.step {
				width: 100%;
				margin-bottom: 40px;
			}
		}
	</style>
</head>
<body style="background-color: rgb(255, 255, 255);" draggable="true">
	<header style="" draggable="true">
		<div class="container" style="">
			<nav style="" draggable="true">
				<div class="logo" style="">MyBuilder</div>
				<ul class="nav-links" style="align-items: center;">
					<li style=""><a href="#features">Features</a></li>
					<li style=""><a href="#how-it-works" style="">How It Works</a></li>
					<li style=""><a href="#testimonials" style="">Testimonials</a></li>
					<li style=""><a href="#pricing" style="">Pricing</a></li>
					<li style="">
						<button href="#" class="btn" onclick="console.log(123)" draggable="true">Start Building</button>
					</li>
				</ul>
			</nav>
		</div>
	</header>

	<section class="hero" style="" draggable="true">
		<div class="container" style="display: flex; flex-direction: column; gap: 50px;" draggable="true">
			<h1 style="font-size: 26px; font-weight: normal;" draggable="true">Build Beautiful Websites Without Code and AI</h1>
			<p style="margin-top: 30px; padding: 40px 20px 10px; font-size: 16px; box-shadow: none; line-height: 2.1;" draggable="true">Create professional websites in minutes with our intuitive drag-and-drop builder. No coding skills required. Just fun.</p>
			<a href="#" class="btn" style="background-color: rgb(102, 140, 255); border-radius: 4px; font-size: 15px;" draggable="true">Try For Free</a>
		</div>
	</section><div style="padding: 16px; border: 1px solid rgb(204, 204, 204); border-radius: 4px; background-color: rgb(249, 250, 251); min-height: 80px; min-width: 120px; display: flex; justify-content: center;" draggable="true"><div style="padding: 0px; border: 0px none rgb(204, 204, 204); border-radius: 4px; background-color: rgb(249, 250, 251); min-height: 80px; min-width: 120px; display: flex; width: 90%; max-width: 1200px; align-items: center; gap: 30px; justify-content: center;" draggable="false" id="element-1748108972561">
			<div style="padding: 0px; font-size: 24px; color: rgb(67, 97, 238); font-weight: 700; flex: 1 1 0%;" draggable="true" id="element-1747677258340">MyBuilder</div>
			<div style="padding: 0px;" draggable="true" id="element-1747677278251">
				<div contenteditable="true" style="width: 100%; height: min-content; outline: none;" draggable="true">Features</div>
			</div>
			<div style="padding: 0px; max-width: none; width: auto;" draggable="true" id="element-1747677283129">How It Works</div>
			<div style="padding: 0px;" draggable="true" id="element-1747677286812">Testimonials</div>
			<div style="padding: 0px;" draggable="true" id="element-1747677295108">Pricing</div>
			<button style="padding: 10px 24px; background-color: rgb(59, 130, 246); color: white; border: none; border-radius: 4px; cursor: pointer; height: 36px;" draggable="true" id="element-1747677297866">Start Building</button>
		</div></div>


	<div style="padding: 150px 16px 100px; border: 1px solid rgb(204, 204, 204); border-radius: 4px; background-color: rgb(81, 45, 224); min-height: 80px; min-width: 120px; display: flex; flex-direction: column; align-items: stretch; gap: 100px; max-width: 1200px;" draggable="true">
		<div style="padding: 8px; color: rgb(255, 255, 255); font-size: 26px; text-align: center;" draggable="true">
			<div contenteditable="true" style="width: 100%; height: min-content; outline: none;" draggable="true">Build Beautiful Websites Without Code and AI</div>
		</div>
		<div style="padding: 8px; font-size: 19px; color: rgb(255, 255, 255);" draggable="true">
			<div contenteditable="true" style="width: 100%; height: min-content; outline: none; text-align: center;" draggable="true">
				<span style="text-align: center;" draggable="true">Create professional websites in minutes with our intuitive drag-and-drop builder. No coding skills required. Just fun.</span>
			</div>
		</div>
		<button style="padding: 8px 16px; background-color: rgb(102, 140, 255); color: white; border: none; border-radius: 4px; cursor: pointer; height: 45px; font-size: 16px;" draggable="true">Try it Free</button>
	</div>
	<div style="padding: 100px 16px; border: 0px solid rgb(204, 204, 204); border-radius: 4px; background-color: rgb(249, 250, 251); min-height: 80px; min-width: 120px; display: flex; flex-direction: column; gap: 50px;" draggable="true" id="element-1748107068698">
		<div style="padding: 0px; text-align: center; font-size: 32px; font-weight: 700;" draggable="true">Why Choose MyBuilder</div>
		<div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 4px; background-color: rgb(249, 250, 251); min-height: 80px; min-width: 120px; display: grid; grid-template-columns: 1fr 1fr; gap: 40px;" draggable="true">
			<div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 8px; background-color: rgb(255, 255, 255); min-height: 80px; min-width: 120px; color: rgb(0, 0, 0); box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 15px 0px;" draggable="true" id="element-1747932869050">
				<div style="padding: 8px; font-size: 23px; font-weight: 700; color: rgb(67, 97, 238);" draggable="true">
					<div contenteditable="true" style="width: 100%; height: min-content; outline: none;" draggable="true">Drag-and-Drop Editor</div>
				</div>
				<div style="padding: 8px;" draggable="true"><div contenteditable="true" style="width: 100%; height: min-content; outline: none;" draggable="true">text textasdasdasd</div></div>
			</div>
			<div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 8px; background-color: rgb(255, 255, 255); min-height: 80px; min-width: 120px; box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 15px 0px;" draggable="true" id="element-1747932874558">
				<div style="padding: 8px; font-weight: 700; font-size: 22px; color: rgb(67, 97, 238);" draggable="true">New Text Component</div>
				<div style="padding: 8px;" draggable="true">New Text Component</div>
			</div>
			<div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 8px; background-color: rgb(255, 255, 255); min-height: 80px; min-width: 120px;" draggable="true" id="element-1747932877620">
				<div style="padding: 8px; font-size: 22px; color: rgb(67, 97, 238); font-weight: 700;" draggable="true">New Text Component</div>
				<div style="padding: 8px;" draggable="true">New Text Component</div>
			</div>
			<div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 8px; background-color: rgb(255, 255, 255); min-height: 80px; min-width: 120px;" draggable="true" id="element-1747932879912">
				<div style="padding: 8px; font-weight: 700; font-size: 22px; color: rgb(67, 97, 238);" draggable="true">New Text Component</div>
				<div style="padding: 8px;">New Text Component</div>
			</div>
			<div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 8px; background-color: rgb(255, 255, 255); min-height: 80px; min-width: 120px;" draggable="true" id="element-1747932882379">
				<div style="padding: 8px; font-weight: 700; font-size: 22px; color: rgb(67, 97, 238);" draggable="true">New Text Component</div>
				<div style="padding: 8px;">New Text Component</div>
			</div>
			<div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 8px; background-color: rgb(255, 255, 255); min-height: 80px; min-width: 120px;" draggable="true">
				<div style="padding: 8px; font-weight: 700; font-size: 22px; color: rgb(67, 97, 238);" draggable="true">New Text Component</div>
				<div style="padding: 8px;" draggable="true">New Text Component</div>
			</div>
		</div>
	</div>

	<div style="padding: 100px 0px; border: 0px solid rgb(204, 204, 204); border-radius: 4px; background-color: rgb(255, 255, 255); min-height: 80px; min-width: 120px;" draggable="true">
		<div style="padding: 8px; text-align: center; font-size: 32px; font-weight: 700;" draggable="true">How It Works</div>
		<div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 4px; background-color: rgba(249, 250, 251, 0); min-height: 80px; min-width: 120px; display: flex; justify-content: space-between;" draggable="true">
			<div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 4px; background-color: rgba(249, 250, 251, 0); min-height: 80px; min-width: 120px; display: flex; justify-content: center; flex-direction: column; align-items: center;" draggable="true">
				<div style="padding: 8px; border-width: 2px; width: 60px; height: 60px; text-align: center; background-color: rgb(33, 48, 255); color: rgb(255, 255, 255); font-size: 24px; border-radius: 50px;" draggable="true">1</div>
				<div style="padding: 8px; font-weight: 700; font-size: 18px;" draggable="true">Choose a Template</div>
				<div style="padding: 8px; text-align: center;" draggable="true">
					<div contenteditable="true" style="width: 100%; height: min-content; outline: none;">Start with one of our professionally designed templates or a blank canvas.</div>
				</div>
			</div>
			<div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 4px; background-color: rgba(249, 250, 251, 0); min-height: 80px; min-width: 120px; display: flex; justify-content: center; flex-direction: column; align-items: center;" draggable="true">
				<div style="padding: 8px; width: 60px; height: 60px; text-align: center; font-size: 24px; background-color: rgb(33, 48, 255); color: rgb(255, 255, 255); border-radius: 50px;" draggable="true">
					<div contenteditable="true" style="width: 100%; height: min-content; outline: none;" draggable="true">2</div>
				</div>
				<div style="padding: 8px; text-align: center; font-weight: 700; font-size: 18px;" draggable="true">Customize Design</div>
				<div style="padding: 8px; text-align: center;" draggable="true">
					<div contenteditable="true" style="width: 100%; height: min-content; outline: none;">
						<span style="text-align: center;" draggable="true">Use our drag-and-drop editor to customize every aspect of your website.</span>
					</div>
				</div>
			</div>
			<div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 4px; background-color: rgba(249, 250, 251, 0); min-height: 80px; min-width: 120px; display: flex; justify-content: center; align-items: center; flex-direction: column;" draggable="true">
				<div style="padding: 8px; width: 60px; text-align: center; color: rgb(255, 255, 255); background-color: rgb(33, 48, 255); border-radius: 50px; font-size: 24px;" draggable="true">
					<div contenteditable="true" style="width: 100%; height: min-content; outline: none;" draggable="true">3</div>
				</div>
				<div style="padding: 8px; font-size: 18px; font-weight: 600;" draggable="true">Publish Your Site</div>
				<div style="padding: 8px; text-align: center;" draggable="true">
					<div contenteditable="true" style="width: 100%; height: min-content; outline: none;">
						<span style="text-align: center;">Once you're satisfied, publish your site with a single click. It's that easy!</span>
					</div>
				</div>
			</div>
		</div>
	</div><div style="padding: 100px 16px; border: 0px solid rgb(204, 204, 204); border-radius: 4px; background-color: rgb(249, 250, 251); min-height: 80px; min-width: 120px; display: flex; flex-direction: column; gap: 50px;" draggable="true" id="element-1748107068698-copy-1748107082429">
		<div style="padding: 0px; text-align: center; font-size: 32px; font-weight: 700;" draggable="true"><div contenteditable="true" style="width: 100%; height: min-content; outline: none;"><span style="font-size: 36px; background-color: rgb(249, 249, 249);" draggable="true">What Our Customers Say</span></div></div>
		<div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 4px; background-color: rgb(249, 250, 251); min-height: 80px; min-width: 120px; display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 40px;" draggable="true">
			<div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 8px; background-color: rgb(255, 255, 255); min-height: 80px; min-width: 120px; color: rgb(0, 0, 0); box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 15px 0px;" draggable="true" id="element-1747932869050-copy-1748107082429">

				<div style="padding: 8px; font-style: italic;" draggable="true">"MyBuilder transformed our business. We built a professional website in just a few hours that would have cost thousands with a traditional agency."</div><div style="padding: 8px; font-weight: 700;" draggable="true">- Sarah Johnson, Freelance Designer</div>
			</div>
			<div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 8px; background-color: rgb(255, 255, 255); min-height: 80px; min-width: 120px;" draggable="true" id="element-1747932874558-copy-1748107082429">

				<div style="padding: 8px; font-style: italic;" draggable="true">"The drag-and-drop interface is so intuitive. I had no web design experience but created a website that looks like it was made by a pro."</div>
			<div style="padding: 8px; font-weight: 700;" draggable="true">- Michael Chen, Restaurant Owner</div></div>
			<div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 8px; background-color: rgb(255, 255, 255); min-height: 80px; min-width: 120px;" draggable="true" id="element-1747932877620-copy-1748107082429">


			<div style="padding: 8px; font-style: italic;" draggable="true">"The responsive design features are a game-changer. My website looks perfect on all devices without any extra work on my part."</div><div style="padding: 8px; font-weight: 700;" draggable="true">- Emma Davis, Startup Founder</div></div>



		</div>
	</div>
	<div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 4px; background-color: rgb(255, 255, 255); min-height: 80px; min-width: 120px;" draggable="true"><div style="padding: 8px; text-align: center; font-weight: 700; font-size: 36px;" draggable="true">Simple, Transparent Pricing</div><div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 4px; background-color: rgba(249, 250, 251, 0); min-height: 80px; min-width: 120px; display: flex; justify-content: space-between; gap: 30px;" draggable="true"><div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 7px; background-color: rgba(249, 250, 251, 0); min-height: 80px; min-width: 120px; display: flex; flex-direction: column; justify-content: center; align-items: center; box-shadow: rgba(0, 0, 0, 0.05) 0px 5px 15px 0px; width: 200%;" draggable="true"><div style="padding: 8px; font-weight: 700;" draggable="true">Starter</div><div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 4px; background-color: rgba(249, 250, 251, 0); min-height: 80px; min-width: 120px; display: flex; align-items: flex-end;" draggable="true"><div style="padding: 0px; font-size: 48px; line-height: 1;" draggable="true">$12</div><div style="padding: 0px; color: rgb(102, 102, 102);" draggable="true">/month</div></div><div style="padding: 8px;" draggable="true">1 Website</div><div style="padding: 8px;" draggable="true">100 Pages</div><div style="padding: 8px;" draggable="true">5GB Storage</div><div style="padding: 8px;" draggable="true">100 Templates</div><div style="padding: 8px;" draggable="true">Standard Support</div><button style="padding: 10px 24px; background-color: rgb(58, 86, 212); color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 50px; height: 46px; font-size: 16px;" draggable="true">Get Started</button></div><div style="padding: 16px; border: 2px solid rgb(67, 97, 238); border-radius: 7px; background-color: rgba(249, 250, 251, 0); min-height: 80px; min-width: 120px; display: flex; flex-direction: column; justify-content: center; align-items: center; box-shadow: rgba(0, 0, 0, 0.05) 0px 5px 15px 0px; width: 200%; position: relative;" draggable="true" id="element-copy-1749117336320"><div style="padding: 8px; font-weight: 700;" draggable="true">Professional</div><div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 4px; background-color: rgba(249, 250, 251, 0); min-height: 80px; min-width: 120px; display: flex; align-items: flex-end;" draggable="true"><div style="padding: 0px; font-size: 48px; line-height: 1;" draggable="true">$29</div><div style="padding: 0px; color: rgb(102, 102, 102);" draggable="true">/month</div></div><div style="padding: 8px;" draggable="true">1 Website</div><div style="padding: 8px;" draggable="true">100 Pages</div><div style="padding: 8px;" draggable="true">5GB Storage</div><div style="padding: 8px;" draggable="true">100 Templates</div><div style="padding: 8px;" draggable="true">Standard Support</div><button style="padding: 10px 24px; background-color: rgb(58, 86, 212); color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 50px; height: 46px; font-size: 16px;" draggable="true">Get Started</button><div style="padding: 5px 15px; position: absolute; margin-top: 0px; right: 25%; background-color: rgb(67, 97, 238); color: rgb(255, 255, 255); text-align: center; border-radius: 50px; left: 25%; font-size: 14px; top: -15px;" draggable="true">Most Popular</div></div><div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 7px; background-color: rgba(249, 250, 251, 0); min-height: 80px; min-width: 120px; display: flex; flex-direction: column; justify-content: center; align-items: center; box-shadow: rgba(0, 0, 0, 0.05) 0px 5px 15px 0px; width: 200%;" draggable="true" id="element-copy-1749117336320-copy-1749117343254"><div style="padding: 8px; font-weight: 700;" draggable="true">Business</div><div style="padding: 16px; border: 0px solid rgb(204, 204, 204); border-radius: 4px; background-color: rgba(249, 250, 251, 0); min-height: 80px; min-width: 120px; display: flex; align-items: flex-end;" draggable="true"><div style="padding: 0px; font-size: 48px; line-height: 1;" draggable="true">$79</div><div style="padding: 0px; color: rgb(102, 102, 102);" draggable="true">/month</div></div><div style="padding: 8px;" draggable="true">1 Website</div><div style="padding: 8px;" draggable="true">100 Pages</div><div style="padding: 8px;" draggable="true">5GB Storage</div><div style="padding: 8px;" draggable="true">100 Templates</div><div style="padding: 8px;" draggable="true">Standard Support</div><button style="padding: 10px 24px; background-color: rgb(58, 86, 212); color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 50px; height: 46px; font-size: 16px;" draggable="true">Get Started</button></div></div></div><section class="features" id="features" style="border-style: none; border-width: 0px;" draggable="true">
		<div class="container" style="" draggable="true">
			<div class="section-title" style="">
				<h2 style="" draggable="true">Why Choose MyBuilder</h2>
			</div>
			<div class="feature-grid" style="" draggable="true">
				<div class="feature-card" style="" draggable="true">
					<h3 style="" draggable="true">Drag-and-Drop Editor</h3>
					<p style="">Build your website visually with our intuitive editor. Just drag elements where you want them.</p>
				</div>
				<div class="feature-card" style="">
					<h3 style="" draggable="true">Responsive Design</h3>
					<p style="">All websites built with MyBuilder look perfect on any device, from desktop to mobile.</p>
				</div>
				<div class="feature-card" style="">
					<h3 style="">Custom Animations</h3>
					<p style="">Add engaging animations to your site without writing a single line of code.</p>
				</div>
				<div class="feature-card" style="">
					<h3 style="">SEO Optimized</h3>
					<p style="">Built-in SEO tools help your website rank higher in search results.</p>
				</div>
				<div class="feature-card">
					<h3>Fast Loading</h3>
					<p>Our optimized code ensures your website loads quickly for all visitors.</p>
				</div>
				<div class="feature-card">
					<h3>Premium Templates</h3>
					<p>Start with professionally designed templates and customize them to match your brand.</p>
				</div>
			</div>
		</div>
	</section>

	<section class="how-it-works" id="how-it-works" style="background-color: rgb(255, 255, 255); border-style: none; border-width: 0px;" draggable="true">
		<div class="container" style="" draggable="true">
			<div class="section-title" style="">
				<h2 style="">How It Works</h2>
			</div>
			<div class="steps" style="" draggable="true">
				<div class="step">
					<div class="step-number" draggable="true">1</div>
					<h3 draggable="true">Choose a Template</h3>
					<p draggable="true">Start with one of our professionally designed templates or a blank canvas.</p>
				</div>
				<div class="step" style="">
					<div class="step-number" style="">2</div>
					<h3 style="">Customize Design</h3>
					<p style="" draggable="true"></p>
					<div contenteditable="true" style="width: 100%; height: min-content; outline: none;">Use our drag-and-drop editor to customize every aspect of your website.</div>
					<p></p>
				</div>
				<div class="step">
					<div class="step-number" draggable="true">3</div>
					<h3>Publish Your Site</h3>
					<p draggable="true"></p>
					<div contenteditable="true" style="width: 100%; height: min-content; outline: none;">Once you're satisfied, publish your site with a single click. It's that easy!</div>
					<p></p>
				</div>
			</div>
		</div>
	</section>

	<section class="testimonials" id="testimonials" style="border-style: none; border-width: 0px;" draggable="true">
		<div class="container" style="" draggable="true">
			<div class="section-title" style="">
				<h2 style="" draggable="true"><div contenteditable="true" style="width: 100%; height: min-content; outline: none;" draggable="true">What Our Customers Say</div></h2>
			</div>
			<div class="testimonial-grid" style="border-style: none; border-width: 0px;" draggable="true">
				<div class="testimonial-card" style="">
					<p class="testimonial-text" style="" draggable="true">"MyBuilder transformed our business. We built a professional website in just a few hours that would have cost thousands with a traditional agency."</p>
					<p class="testimonial-author" style="" draggable="true">- Sarah Johnson, Freelance Designer</p>
				</div>
				<div class="testimonial-card" style="" draggable="true">
					<p class="testimonial-text" draggable="true">"The drag-and-drop interface is so intuitive. I had no web design experience but created a website that looks like it was made by a pro."</p>
					<p class="testimonial-author" draggable="true">- Michael Chen, Restaurant Owner</p>
				</div>
				<div class="testimonial-card" style="">
					<p class="testimonial-text" style="" draggable="true">"The responsive design features are a game-changer. My website looks perfect on all devices without any extra work on my part."</p>
					<p class="testimonial-author" draggable="true">- Emma Davis, Startup Founder</p>
				</div>
			</div>
		</div>
	</section>

	<section class="pricing" id="pricing" style="background-color: rgb(255, 255, 255);" draggable="true">
		<div class="container" style="" draggable="true">
			<div class="section-title">
				<h2 draggable="true">Simple, Transparent Pricing</h2>
			</div>
			<div class="pricing-grid" style="" draggable="true">
				<div class="pricing-card" style="" draggable="true">
					<h3 draggable="true">Starter</h3>
					<div class="price" draggable="true">$12<span draggable="true">/month</span></div>
					<ul class="pricing-features" style="" draggable="true">
						<li>1 Website</li>
						<li>100 Pages</li>
						<li>5GB Storage</li>
						<li>Basic Templates</li>
						<li style="">Standard Support</li>
					</ul>
					<a href="#" class="btn" style="" draggable="true">Get Started</a>
				</div>
				<div class="pricing-card featured" style="" draggable="true">
					<div class="pricing-tag" draggable="true">Most Popular</div>
					<h3 style="" draggable="true">Professional</h3>
					<div class="price" style="">$29<span>/month</span></div>
					<ul class="pricing-features">
						<li>5 Websites</li>
						<li>Unlimited Pages</li>
						<li>20GB Storage</li>
						<li>Premium Templates</li>
						<li>Priority Support</li>
					</ul>
					<a href="#" class="btn" style="" draggable="true">Get Started</a>
				</div>
				<div class="pricing-card" style="" draggable="true">
					<h3 style="">Business</h3>
					<div class="price">$79<span>/month</span></div>
					<ul class="pricing-features">
						<li>20 Websites</li>
						<li>Unlimited Pages</li>
						<li>100GB Storage</li>
						<li>All Templates</li>
						<li>24/7 Support</li>
					</ul>
					<a href="#" class="btn" draggable="true">Get Started</a>
				</div>
			</div>
		</div>
	</section>

	<section class="cta" style="" draggable="true">
		<div class="container" style="" draggable="true">
			<h2>Ready to Build Your Website?</h2>
			<p style="">Start creating your professional website today with our easy-to-use builder. No credit card required for the free trial.</p>
			<button href="#" class="btn" style="">Start Building Now</button>
		</div>
	</section>

	<footer style="" draggable="true">
		<div class="container" style="">
			<div class="footer-content" style="" draggable="true">
				<div class="footer-column">
					<h3>MyBuilder</h3>
					<p>Build beautiful websites without code. MyBuilder empowers everyone to create professional websites with our intuitive drag-and-drop builder.</p>
				</div>
				<div class="footer-column" style="">
					<h3 style="">Product</h3>
					<ul class="footer-links">
						<li><a href="#">Features</a></li>
						<li><a href="#">Templates</a></li>
						<li><a href="#">Pricing</a></li>
						<li><a href="#">Updates</a></li>
					</ul>
				</div>
				<div class="footer-column">
					<h3>Company</h3>
					<ul class="footer-links">
						<li><a href="#">About Us</a></li>
						<li><a href="#">Careers</a></li>
						<li><a href="#">Blog</a></li>
						<li><a href="#">Press</a></li>
					</ul>
				</div>
				<div class="footer-column">
					<h3>Support</h3>
					<ul class="footer-links">
						<li><a href="#">Help Center</a></li>
						<li><a href="#">Community</a></li>
						<li><a href="#">Contact Us</a></li>
					</ul>
				</div>
			</div>
			<div class="copyright" style="">
				© 2023 MyBuilder. All rights reserved.
			</div>
		</div>
	</footer>








</body></html>