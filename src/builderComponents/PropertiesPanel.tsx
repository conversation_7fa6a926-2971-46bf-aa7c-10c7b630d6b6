import { Key, useCallback, useEffect, useRef, useState } from 'react'
import { JollyTextField } from '@/components/ui/textfield'
import { JollySelect, SelectItem } from '@/components/ui/select'
import { Slider, SliderFillTrack, SliderThumb, SliderTrack } from '@/components/ui/slider'
import { HexColorPicker } from '@/components/ui/hexColorPicker'
import { Disclosure, DisclosureHeader, DisclosurePanel } from '@/components/ui/disclosure'
import { Tabs, TabList, Tab, TabPanel } from '@/components/ui/tabs'
import {
    BoxShadowPositionValue,
    getDimensionStyle,
    getOriginalStyleValue,
    getStyleValue,
    parseBoxShadow
} from '@/lib/builder/styleUtils'

interface PropertiesPanelProps {
    selectedElement?: HTMLElement;
    onStyleChange: () => void;
    onAttributeChange?: (attribute: string, value: string) => void;
}

type SpacingValues = {
    top: string;
    right: string;
    bottom: string;
    left: string;
};

type DisplayValue = 'block' | 'flex' | 'grid' | '';
type FlexDirectionValue = 'row' | 'column' | '';
type JustifyContentValue =
    'flex-start'
    | 'flex-end'
    | 'center'
    | 'space-between'
    | 'space-around'
    | 'space-evenly'
    | '';
type AlignItemsValue = 'start' | 'end' | 'center' | 'stretch' | '';
type FontWeightValue = 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900' | '';
type FontStyleValue = 'normal' | 'italic' | '';
type BorderStyleValue = 'none' | 'solid' | 'dashed' | 'dotted' | 'double' | '';
type PositionValue = 'static' | 'relative' | 'absolute' | 'fixed' | 'sticky' | '';
type TextAlignValue = 'left' | 'center' | 'right' | 'justify' | '';

const PropertiesPanel = ({selectedElement, onStyleChange, onAttributeChange}: PropertiesPanelProps) => {
    // Use refs to track current values without triggering re-renders
    const styleValuesRef = useRef({
        margin: {top: '', right: '', bottom: '', left: ''},
        padding: {top: '', right: '', bottom: '', left: ''},
        display: '' as DisplayValue,
        gridTemplateColumns: '',
        gridTemplateRows: '',
        flexDirection: '' as FlexDirectionValue,
        justifyContent: '' as JustifyContentValue,
        alignItems: '' as AlignItemsValue,
        gap: '',
        gapValue: 0,
        width: '',
        height: '',
        maxWidth: '',
        maxHeight: '',
        fontWeight: '' as FontWeightValue,
        fontSize: '',
        fontSizeValue: 16,
        lineHeight: '',
        lineHeightValue: 1.5,
        backgroundColor: '',
        borderRadius: '',
        borderRadiusValue: 0,
        borderWidth: '',
        borderWidthValue: 0,
        borderColor: '',
        borderStyle: '' as BorderStyleValue,
        color: '',
        fontStyle: '' as FontStyleValue,
        position: '' as PositionValue,
        top: '',
        right: '',
        bottom: '',
        left: '',
        textAlign: '' as TextAlignValue,
        boxShadow: '',
        boxShadowOffsetX: 0,
        boxShadowOffsetY: 0,
        boxShadowBlur: 0,
        boxShadowSpread: 0,
        boxShadowColor: '',
        boxShadowPosition: '' as BoxShadowPositionValue
    })

    // Use state for display-only purposes and active input tracking
    const [displayMode, setDisplayMode] = useState<DisplayValue>('')
    const [rerender, setRerender] = useState(0) // Use to force re-render when needed

    // Add controlled state for active input fields
    const [activeInputs, setActiveInputs] = useState({
        gridTemplateColumns: '',
        gridTemplateRows: '',
        gap: '',
        width: '',
        height: '',
        maxWidth: '',
        maxHeight: '',
        margin: {top: '', right: '', bottom: '', left: ''},
        padding: {top: '', right: '', bottom: '', left: ''},
        top: '',
        right: '',
        bottom: '',
        left: ''
    })

    // Add state for HTML attributes
    const [htmlAttributes, setHtmlAttributes] = useState({
        id: ''
    })

    // Debounced color change handler to improve performance
    const debouncedColorChange = useCallback((property: 'backgroundColor' | 'color' | 'borderColor' | 'boxShadowColor', value: string) => {
        if (!selectedElement) return

        if (property === 'boxShadowColor') {
            // Handle box shadow color specially - need to update the complete box-shadow property
            const values = styleValuesRef.current
            const shadowValue = `${values.boxShadowPosition === 'inset' ? 'inset ' : ''}${values.boxShadowOffsetX}px ${values.boxShadowOffsetY}px ${values.boxShadowBlur}px ${values.boxShadowSpread}px ${value}`
            selectedElement.style.boxShadow = shadowValue
            values.boxShadow = shadowValue
            values.boxShadowColor = value
        } else {
            (selectedElement.style as any)[property] = value;
            (styleValuesRef.current as any)[property] = value
        }
        onStyleChange()
    }, [onStyleChange, selectedElement])

    // Load styles from element when it changes
    useEffect(() => {
        if (selectedElement) {
            const styles = window.getComputedStyle(selectedElement)
            const values = styleValuesRef.current

            // Spacing values - try to get original values first
            values.margin = {
                top: getStyleValue(selectedElement, 'marginTop', styles),
                right: getStyleValue(selectedElement, 'marginRight', styles),
                bottom: getStyleValue(selectedElement, 'marginBottom', styles),
                left: getStyleValue(selectedElement, 'marginLeft', styles),
            }

            values.padding = {
                top: getStyleValue(selectedElement, 'paddingTop', styles),
                right: getStyleValue(selectedElement, 'paddingRight', styles),
                bottom: getStyleValue(selectedElement, 'paddingBottom', styles),
                left: getStyleValue(selectedElement, 'paddingLeft', styles),
            }

            // Layout values
            const originalDisplay = getOriginalStyleValue(selectedElement, 'display')
            const currentDisplay = (originalDisplay || styles.display) as DisplayValue
            values.display = currentDisplay === 'flex' || currentDisplay === 'grid' ? currentDisplay : 'block'
            setDisplayMode(values.display) // Need to update state for conditional rendering

            values.gridTemplateColumns = getStyleValue(selectedElement, 'gridTemplateColumns', styles)
            values.gridTemplateRows = getStyleValue(selectedElement, 'gridTemplateRows', styles)
            values.flexDirection = (getStyleValue(selectedElement, 'flexDirection', styles) as FlexDirectionValue) || 'row'
            values.justifyContent = (getStyleValue(selectedElement, 'justifyContent', styles) as JustifyContentValue) || 'flex-start'
            values.alignItems = (getStyleValue(selectedElement, 'alignItems', styles) as AlignItemsValue) || 'stretch'
            values.gap = getStyleValue(selectedElement, 'gap', styles)
            values.gapValue = parseInt(styles.gap) || 0

            // Position values
            values.position = (getStyleValue(selectedElement, 'position', styles) as PositionValue) || 'static'
            values.top = getStyleValue(selectedElement, 'top', styles)
            values.right = getStyleValue(selectedElement, 'right', styles)
            values.bottom = getStyleValue(selectedElement, 'bottom', styles)
            values.left = getStyleValue(selectedElement, 'left', styles)

            // Typography values
            values.fontWeight = (getStyleValue(selectedElement, 'fontWeight', styles) as FontWeightValue) || 'normal'
            values.fontSize = getStyleValue(selectedElement, 'fontSize', styles)
            values.fontSizeValue = parseInt(styles.fontSize) || 16
            values.lineHeight = getStyleValue(selectedElement, 'lineHeight', styles)
            values.lineHeightValue = parseFloat(styles.lineHeight) || 1.5
            values.color = styles.color
            values.fontStyle = (getStyleValue(selectedElement, 'fontStyle', styles) as FontStyleValue) || 'normal'
            values.textAlign = (getStyleValue(selectedElement, 'textAlign', styles) as TextAlignValue) || ''

            // Dimension values - use special function to detect percentages
            values.width = getDimensionStyle(selectedElement, 'width')
            values.height = getDimensionStyle(selectedElement, 'height')
            values.maxWidth = getDimensionStyle(selectedElement, 'maxWidth')
            values.maxHeight = getDimensionStyle(selectedElement, 'maxHeight')

            // Appearance values
            values.backgroundColor = styles.backgroundColor
            values.borderRadius = getStyleValue(selectedElement, 'borderRadius', styles)
            values.borderRadiusValue = parseInt(styles.borderRadius) || 0
            values.borderWidth = styles.borderWidth //getStyleValue(selectedElement, 'borderWidth', styles)
            values.borderWidthValue = parseInt(values.borderWidth) || 0
            values.borderColor = styles.borderColor
            values.borderStyle = (getStyleValue(selectedElement, 'borderStyle', styles) as BorderStyleValue) || 'none'

            // Box shadow values
            const boxShadowValue = getStyleValue(selectedElement, 'boxShadow', styles)
            const parsedShadow = parseBoxShadow(boxShadowValue)
            values.boxShadow = boxShadowValue
            values.boxShadowOffsetX = parsedShadow.offsetX
            values.boxShadowOffsetY = parsedShadow.offsetY
            values.boxShadowBlur = parsedShadow.blur
            values.boxShadowSpread = parsedShadow.spread
            values.boxShadowColor = parsedShadow.color
            values.boxShadowPosition = parsedShadow.position

            // Update activeInputs state for controlled inputs
            setActiveInputs({
                gridTemplateColumns: values.gridTemplateColumns,
                gridTemplateRows: values.gridTemplateRows,
                gap: values.gap,
                width: values.width,
                height: values.height,
                maxWidth: values.maxWidth,
                maxHeight: values.maxHeight,
                margin: {...values.margin},
                padding: {...values.padding},
                top: values.top,
                right: values.right,
                bottom: values.bottom,
                left: values.left
            })

            // Load HTML attributes
            setHtmlAttributes({
                id: selectedElement.id || ''
            })

            // Force a re-render to update the UI with new values
            setRerender(prev => prev + 1)
        } else {
            // Reset values when no element is selected
            const values = styleValuesRef.current
            values.margin = {top: '', right: '', bottom: '', left: ''}
            values.padding = {top: '', right: '', bottom: '', left: ''}
            values.display = ''
            setDisplayMode('')
            values.gridTemplateColumns = ''
            values.gridTemplateRows = ''
            values.flexDirection = ''
            values.justifyContent = ''
            values.alignItems = ''
            values.gap = ''
            values.gapValue = 0
            values.fontWeight = ''
            values.fontSize = ''
            values.fontSizeValue = 16
            values.lineHeight = ''
            values.lineHeightValue = 1.5
            values.backgroundColor = ''
            values.borderRadius = ''
            values.borderRadiusValue = 0
            values.borderWidth = ''
            values.borderWidthValue = 0
            values.borderColor = ''
            values.borderStyle = ''
            values.color = ''
            values.width = ''
            values.height = ''
            values.maxWidth = ''
            values.maxHeight = ''
            values.fontStyle = ''
            values.position = ''
            values.top = ''
            values.right = ''
            values.bottom = ''
            values.left = ''
            values.textAlign = ''
            values.boxShadow = ''
            values.boxShadowOffsetX = 0
            values.boxShadowOffsetY = 0
            values.boxShadowBlur = 0
            values.boxShadowSpread = 0
            values.boxShadowColor = ''
            values.boxShadowPosition = ''

            // Reset activeInputs state
            setActiveInputs({
                gridTemplateColumns: '',
                gridTemplateRows: '',
                gap: '',
                width: '',
                height: '',
                maxWidth: '',
                maxHeight: '',
                margin: {top: '', right: '', bottom: '', left: ''},
                padding: {top: '', right: '', bottom: '', left: ''},
                top: '',
                right: '',
                bottom: '',
                left: ''
            })

            // Force a re-render to update the UI
            setRerender(prev => prev + 1)
        }
    }, [selectedElement])

    // Update style without changing state
    const updateStyle = useCallback((property: keyof CSSStyleDeclaration, value: string) => {
        if (!selectedElement) return;
        (selectedElement.style as any)[property] = value;

        // Update ref value but don't trigger a re-render
        (styleValuesRef.current as any)[property] = value
        onStyleChange() // Only save to file when an input loses focus
    }, [onStyleChange, selectedElement])

    // Handle blur - only save changes on blur
    const handleInputBlur = useCallback(() => {
        // if (selectedElement) {
        //   onStyleChange(); // Only save to file when an input loses focus
        // }
    }, [selectedElement, onStyleChange])

    // Handle margin/padding changes
    const handleSpacingChange = useCallback((
        type: 'margin' | 'padding',
        direction: keyof SpacingValues,
        value: string
    ) => {
        if (!selectedElement) return
        const sanitizedValue = value.match(/^[0-9.]+(px|em|rem|%)?$/) ? value : ''
        const cssProperty = `${type}${direction.charAt(0).toUpperCase() + direction.slice(1)}` as keyof CSSStyleDeclaration

        // Update the DOM style directly
        updateStyle(cssProperty, sanitizedValue)

        // Update the ref without triggering re-render
        styleValuesRef.current[type][direction] = sanitizedValue

        // Update the controlled state for this specific input
        setActiveInputs(prev => ({
            ...prev,
            [type]: {
                ...prev[type],
                [direction]: value // Use the original input value for controlled input
            }
        }))
    }, [selectedElement, updateStyle])

    // Handle position values changes
    const handlePositionChange = useCallback((
        property: 'top' | 'right' | 'bottom' | 'left',
        value: string
    ) => {
        if (!selectedElement) return
        const sanitizedValue = value.match(/^[0-9.-]+(px|em|rem|%)?$/) ? value : ''

        // Update the DOM style directly
        updateStyle(property, sanitizedValue)

        // Update the ref without triggering re-render
        styleValuesRef.current[property] = sanitizedValue

        // Update the controlled state for this specific input
        setActiveInputs(prev => ({
            ...prev,
            [property]: value // Use the original input value for controlled input
        }))
    }, [selectedElement, updateStyle])

    // Handle box shadow changes
    const handleBoxShadowChange = useCallback((
        property: 'offsetX' | 'offsetY' | 'blur' | 'spread' | 'position',
        value: number | string
    ) => {
        if (!selectedElement) return

        const values = styleValuesRef.current

        // Update the specific property
        switch (property) {
            case 'offsetX':
                values.boxShadowOffsetX = value as number
                break
            case 'offsetY':
                values.boxShadowOffsetY = value as number
                break
            case 'blur':
                values.boxShadowBlur = value as number
                break
            case 'spread':
                values.boxShadowSpread = value as number
                break
            case 'position':
                values.boxShadowPosition = value as BoxShadowPositionValue
                break
        }

        // Construct the complete box-shadow value
        const shadowValue = values.boxShadowOffsetX === 0 && values.boxShadowOffsetY === 0 && values.boxShadowBlur === 0 && values.boxShadowSpread === 0
            ? 'none'
            : `${values.boxShadowPosition === 'inset' ? 'inset ' : ''}${values.boxShadowOffsetX}px ${values.boxShadowOffsetY}px ${values.boxShadowBlur}px ${values.boxShadowSpread}px ${values.boxShadowColor || 'rgba(0, 0, 0, 0.1)'}`

        // Update the DOM and ref
        selectedElement.style.boxShadow = shadowValue
        values.boxShadow = shadowValue

        // Force re-render for slider UI
        setRerender(prev => prev + 1)

        // Save changes
        onStyleChange()
    }, [selectedElement, onStyleChange])

    // Handle select changes
    const handleSelectChange = useCallback((
        key: React.Key,
        property: keyof typeof styleValuesRef.current,
        styleProperty: keyof CSSStyleDeclaration
    ) => {
        const newValue = key as string

        // Update the DOM style directly
        updateStyle(styleProperty, newValue);

        // Update the ref
        (styleValuesRef.current as any)[property] = newValue

        // Special case for display to update conditional rendering
        if (property === 'display') {
            setDisplayMode(newValue as DisplayValue)
        }

        // Select changes are discrete actions, save right away
        onStyleChange()
    }, [updateStyle, onStyleChange])

    // Handle slider changes
    const handleSliderChange = useCallback((
        value: number,
        unit: string,
        valueProperty: keyof typeof styleValuesRef.current,
        styleProperty: keyof CSSStyleDeclaration
    ) => {
        // Create the string value (e.g., "16px")
        const stringValue = `${value}${unit}`

        // Update the DOM style directly
        updateStyle(styleProperty, stringValue);

        // Update the refs
        (styleValuesRef.current as any)[valueProperty + 'Value'] = value;
        (styleValuesRef.current as any)[valueProperty] = stringValue

        // Special handling for gap to also update activeInputs
        if (valueProperty === 'gap') {
            setActiveInputs(prev => ({...prev, gap: stringValue}))
        }

        // Force re-render for slider UI
        setRerender(prev => prev + 1)
    }, [updateStyle])

    const displayOptions = [
        {id: 'block', name: 'Block'},
        {id: 'flex', name: 'Flex'},
        {id: 'grid', name: 'Grid'},
    ]

    const flexDirectionOptions = [
        {id: 'row', name: 'Row'},
        {id: 'column', name: 'Column'},
    ]

    const justifyContentOptions = [
        {id: 'flex-start', name: 'Start'},
        {id: 'flex-end', name: 'End'},
        {id: 'center', name: 'Center'},
        {id: 'space-between', name: 'Space Between'},
        {id: 'space-around', name: 'Space Around'},
        {id: 'space-evenly', name: 'Space Evenly'},
    ]

    const alignItemsOptions = [
        {id: 'flex-start', name: 'Start'},
        {id: 'flex-end', name: 'End'},
        {id: 'center', name: 'Center'},
        {id: 'stretch', name: 'Stretch'},
        {id: 'baseline', name: 'Baseline'},
    ]

    const fontWeightOptions = [
        {id: 'normal', name: 'Normal'},
        {id: '100', name: 'Thin (100)'},
        {id: '200', name: 'Extra Light (200)'},
        {id: '300', name: 'Light (300)'},
        {id: '400', name: 'Regular (400)'},
        {id: '500', name: 'Medium (500)'},
        {id: '600', name: 'Semi Bold (600)'},
        {id: '700', name: 'Bold (700)'},
        {id: '800', name: 'Extra Bold (800)'},
        {id: '900', name: 'Black (900)'},
    ]

    const fontStyleOptions = [
        {id: 'normal', name: 'Normal'},
        {id: 'italic', name: 'Italic'},
    ]

    const borderStyleOptions = [
        {id: 'none', name: 'None'},
        {id: 'solid', name: 'Solid'},
        {id: 'dashed', name: 'Dashed'},
        {id: 'dotted', name: 'Dotted'},
        {id: 'double', name: 'Double'},
    ]

    const positionOptions = [
        {id: 'static', name: 'Static'},
        {id: 'relative', name: 'Relative'},
        {id: 'absolute', name: 'Absolute'},
        {id: 'fixed', name: 'Fixed'},
        {id: 'sticky', name: 'Sticky'},
    ]

    const textAlignOptions = [
        {id: 'left', name: 'Left'},
        {id: 'center', name: 'Center'},
        {id: 'right', name: 'Right'},
        {id: 'justify', name: 'Justify'},
    ]

    const boxShadowPositionOptions = [
        {id: 'outset', name: 'Outset (Drop Shadow)'},
        {id: 'inset', name: 'Inset (Inner Shadow)'},
    ]

    // Get current values from ref for rendering
    const {
        margin,
        padding,
        display,
        gridTemplateColumns,
        gridTemplateRows,
        flexDirection,
        justifyContent,
        alignItems,
        gap,
        gapValue,
        fontWeight,
        fontSize,
        fontSizeValue,
        lineHeight,
        lineHeightValue,
        backgroundColor,
        borderRadius,
        borderRadiusValue,
        borderWidth,
        borderWidthValue,
        borderColor,
        borderStyle,
        color,
        width,
        height,
        maxWidth,
        maxHeight,
        fontStyle,
        position,
        top,
        right,
        bottom,
        left,
        textAlign,
        boxShadow,
        boxShadowOffsetX,
        boxShadowOffsetY,
        boxShadowBlur,
        boxShadowSpread,
        boxShadowColor,
        boxShadowPosition
    } = styleValuesRef.current

    return (
        <div className="w-full h-full py-2 overflow-y-auto flex flex-col gap-4 relative">
            <div className="flex flex-col gap-2 px-2">
                <h3 className="text-lg font-semibold">Properties</h3>
                {selectedElement && (
                    <p className="text-sm text-muted-foreground">
                        <strong>Tag:</strong> {selectedElement.tagName.toLowerCase()}
                    </p>
                )}
            </div>

            {selectedElement ? (
                <div>

                    {/* Size & Dimensions */}
                    <Disclosure defaultExpanded className="px-2 border-0 border-b border-b-secondary">
                        <DisclosureHeader className="font-semibold">Size & Dimensions</DisclosureHeader>
                        <DisclosurePanel className="pl-3">
                            <div className="flex flex-col gap-3">
                                <div className="grid grid-cols-2 gap-2">
                                    <JollyTextField
                                        label="Width"
                                        aria-label="Width"
                                        value={width}
                                        onChange={(v) => {
                                            updateStyle('width', v)
                                            styleValuesRef.current.width = v
                                            setActiveInputs(prev => ({...prev, width: v}))
                                        }}
                                        onBlur={handleInputBlur}
                                    />
                                    <JollyTextField
                                        label="Height"
                                        aria-label="Height"
                                        value={height}
                                        onChange={(v) => {
                                            updateStyle('height', v)
                                            styleValuesRef.current.height = v
                                            setActiveInputs(prev => ({...prev, height: v}))
                                        }}
                                        onBlur={handleInputBlur}
                                    />
                                </div>
                                <div className="grid grid-cols-2 gap-2">
                                    <JollyTextField
                                        label="Max width"
                                        aria-label="Max width"
                                        value={maxWidth}
                                        onChange={(v) => {
                                            updateStyle('maxWidth', v)
                                            styleValuesRef.current.maxWidth = v
                                            setActiveInputs(prev => ({...prev, maxWidth: v}))
                                        }}
                                        onBlur={handleInputBlur}
                                    />
                                    <JollyTextField
                                        label="Max height"
                                        aria-label="Max height"
                                        value={maxHeight}
                                        onChange={(v) => {
                                            updateStyle('maxHeight', v)
                                            styleValuesRef.current.maxHeight = v
                                            setActiveInputs(prev => ({...prev, maxHeight: v}))
                                        }}
                                        onBlur={handleInputBlur}
                                    />
                                </div>
                            </div>
                        </DisclosurePanel>
                    </Disclosure>

                    {/* Layout */}
                    <Disclosure defaultExpanded className="px-2 border-0 border-b border-b-secondary">
                        <DisclosureHeader className="font-semibold">Layout</DisclosureHeader>
                        <DisclosurePanel className="pl-3">
                            <div className="flex flex-col gap-4">
                                {/* General Layout */}
                                <div>
                                    <h5 className="text-sm font-medium mb-2">General</h5>
                                    <div className="flex flex-col gap-3">
                                        <div className="grid grid-cols-2 gap-2">
                                            <JollySelect
                                                label="Display"
                                                aria-label="Display"
                                                items={displayOptions}
                                                selectedKey={display}
                                                onSelectionChange={(key) => handleSelectChange(key, 'display', 'display')}
                                            >
                                                {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                                            </JollySelect>

                                            <JollySelect
                                                label="Position"
                                                aria-label="Position"
                                                items={positionOptions}
                                                selectedKey={position}
                                                onSelectionChange={(key) => handleSelectChange(key, 'position', 'position')}
                                            >
                                                {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                                            </JollySelect>
                                        </div>

                                        <div>
                                            <JollyTextField
                                                label="Gap"
                                                aria-label="Gap"
                                                value={activeInputs.gap}
                                                onChange={(v) => {
                                                    updateStyle('gap', v)
                                                    styleValuesRef.current.gap = v
                                                    setActiveInputs(prev => ({...prev, gap: v}))
                                                }}
                                                onBlur={handleInputBlur}
                                            />
                                            <div className="flex flex-col gap-1 mt-2">
                                                <label className="text-sm font-medium">Gap: {gapValue}px</label>
                                                <Slider
                                                    value={gapValue}
                                                    defaultValue={gapValue}
                                                    onChange={(value) => handleSliderChange(value as number, 'px', 'gap', 'gap')}
                                                    onChangeEnd={() => handleInputBlur()}
                                                    minValue={0}
                                                    maxValue={100}
                                                    step={1}
                                                    aria-label="Gap"
                                                >
                                                    <SliderTrack>
                                                        <SliderFillTrack/>
                                                    </SliderTrack>
                                                    <SliderThumb/>
                                                </Slider>
                                            </div>
                                        </div>

                                        {position !== 'static' && position !== '' && (
                                            <div className="grid grid-cols-2 gap-2">
                                                <JollyTextField
                                                    label="Top"
                                                    aria-label="Position Top"
                                                    value={activeInputs.top}
                                                    onChange={(v) => handlePositionChange('top', v)}
                                                    onBlur={handleInputBlur}
                                                />
                                                <JollyTextField
                                                    label="Right"
                                                    aria-label="Position Right"
                                                    value={activeInputs.right}
                                                    onChange={(v) => handlePositionChange('right', v)}
                                                    onBlur={handleInputBlur}
                                                />
                                                <JollyTextField
                                                    label="Bottom"
                                                    aria-label="Position Bottom"
                                                    value={activeInputs.bottom}
                                                    onChange={(v) => handlePositionChange('bottom', v)}
                                                    onBlur={handleInputBlur}
                                                />
                                                <JollyTextField
                                                    label="Left"
                                                    aria-label="Position Left"
                                                    value={activeInputs.left}
                                                    onChange={(v) => handlePositionChange('left', v)}
                                                    onBlur={handleInputBlur}
                                                />
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Flex Layout */}
                                {displayMode === 'flex' && (
                                    <div>
                                        <h5 className="text-sm font-medium mb-2">Flex Layout</h5>
                                        <div className="flex flex-col gap-3">
                                            <JollySelect
                                                label="Flex Direction"
                                                aria-label="Flex Direction"
                                                items={flexDirectionOptions}
                                                selectedKey={flexDirection}
                                                onSelectionChange={(key) => handleSelectChange(key, 'flexDirection', 'flexDirection')}
                                            >
                                                {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                                            </JollySelect>
                                            <JollySelect
                                                label="Justify Content"
                                                aria-label="Justify Content"
                                                items={justifyContentOptions}
                                                selectedKey={justifyContent}
                                                onSelectionChange={(key) => handleSelectChange(key, 'justifyContent', 'justifyContent')}
                                            >
                                                {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                                            </JollySelect>
                                            <JollySelect
                                                label="Align Items"
                                                aria-label="Align Items"
                                                items={alignItemsOptions}
                                                selectedKey={alignItems}
                                                onSelectionChange={(key) => handleSelectChange(key, 'alignItems', 'alignItems')}
                                            >
                                                {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                                            </JollySelect>
                                        </div>
                                    </div>
                                )}

                                {/* Grid Layout */}
                                {displayMode === 'grid' && (
                                    <div>
                                        <h5 className="text-sm font-medium mb-2">Grid Layout</h5>
                                        <div className="flex flex-col gap-3">
                                            <JollyTextField
                                                label="Grid Columns"
                                                aria-label="Grid Columns"
                                                value={activeInputs.gridTemplateColumns}
                                                onChange={(v) => {
                                                    updateStyle('gridTemplateColumns', v)
                                                    styleValuesRef.current.gridTemplateColumns = v
                                                    setActiveInputs(prev => ({...prev, gridTemplateColumns: v}))
                                                }}
                                                onBlur={handleInputBlur}
                                            />
                                            <JollyTextField
                                                label="Grid Rows"
                                                aria-label="Grid Rows"
                                                value={activeInputs.gridTemplateRows}
                                                onChange={(v) => {
                                                    updateStyle('gridTemplateRows', v)
                                                    styleValuesRef.current.gridTemplateRows = v
                                                    setActiveInputs(prev => ({...prev, gridTemplateRows: v}))
                                                }}
                                                onBlur={handleInputBlur}
                                            />
                                        </div>
                                    </div>
                                )}
                            </div>
                        </DisclosurePanel>
                    </Disclosure>

                    {/* Spacing */}
                    <Disclosure defaultExpanded className="px-2 border-0 border-b border-b-secondary">
                        <DisclosureHeader className="font-semibold">Spacing</DisclosureHeader>
                        <DisclosurePanel className="pl-3">
                            <div className="flex flex-col gap-4">
                                <div>
                                    <h5 className="text-sm font-medium mb-2">Margin</h5>
                                    <div className="grid grid-cols-2 gap-2">
                                        <JollyTextField
                                            label="Top"
                                            aria-label="Margin Top"
                                            value={activeInputs.margin.top}
                                            onChange={(v) => handleSpacingChange('margin', 'top', v)}
                                            onBlur={handleInputBlur}
                                        />
                                        <JollyTextField
                                            label="Right"
                                            aria-label="Margin Right"
                                            value={activeInputs.margin.right}
                                            onChange={(v) => handleSpacingChange('margin', 'right', v)}
                                            onBlur={handleInputBlur}
                                        />
                                        <JollyTextField
                                            label="Bottom"
                                            aria-label="Margin Bottom"
                                            value={activeInputs.margin.bottom}
                                            onChange={(v) => handleSpacingChange('margin', 'bottom', v)}
                                            onBlur={handleInputBlur}
                                        />
                                        <JollyTextField
                                            label="Left"
                                            aria-label="Margin Left"
                                            value={activeInputs.margin.left}
                                            onChange={(v) => handleSpacingChange('margin', 'left', v)}
                                            onBlur={handleInputBlur}
                                        />
                                    </div>
                                </div>

                                <div>
                                    <h5 className="text-sm font-medium mb-2">Padding</h5>
                                    <div className="grid grid-cols-2 gap-2">
                                        <JollyTextField
                                            label="Top"
                                            aria-label="Padding Top"
                                            value={activeInputs.padding.top}
                                            onChange={(v) => handleSpacingChange('padding', 'top', v)}
                                            onBlur={handleInputBlur}
                                        />
                                        <JollyTextField
                                            label="Right"
                                            aria-label="Padding Right"
                                            value={activeInputs.padding.right}
                                            onChange={(v) => handleSpacingChange('padding', 'right', v)}
                                            onBlur={handleInputBlur}
                                        />
                                        <JollyTextField
                                            label="Bottom"
                                            aria-label="Padding Bottom"
                                            value={activeInputs.padding.bottom}
                                            onChange={(v) => handleSpacingChange('padding', 'bottom', v)}
                                            onBlur={handleInputBlur}
                                        />
                                        <JollyTextField
                                            label="Left"
                                            aria-label="Padding Left"
                                            value={activeInputs.padding.left}
                                            onChange={(v) => handleSpacingChange('padding', 'left', v)}
                                            onBlur={handleInputBlur}
                                        />
                                    </div>
                                </div>
                            </div>
                        </DisclosurePanel>
                    </Disclosure>

                    {/* Typography */}
                    <Disclosure defaultExpanded className="px-2 border-0 border-b border-b-secondary">
                        <DisclosureHeader className="font-semibold">Typography</DisclosureHeader>
                        <DisclosurePanel className="pl-3">
                            <div className="flex flex-col gap-3">
                                <JollySelect
                                    label="Font Weight"
                                    aria-label="Font Weight"
                                    items={fontWeightOptions}
                                    selectedKey={fontWeight}
                                    onSelectionChange={(key) => handleSelectChange(key, 'fontWeight', 'fontWeight')}
                                >
                                    {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                                </JollySelect>

                                <div className="flex flex-col gap-1">
                                    <label className="text-sm font-medium">Font Size: {fontSizeValue}px</label>
                                    <Slider
                                        value={fontSizeValue}
                                        defaultValue={fontSizeValue}
                                        onChange={(value) => handleSliderChange(value as number, 'px', 'fontSize', 'fontSize')}
                                        onChangeEnd={() => handleInputBlur()}
                                        minValue={4}
                                        maxValue={72}
                                        step={1}
                                        aria-label="Font Size"
                                    >
                                        <SliderTrack>
                                            <SliderFillTrack/>
                                        </SliderTrack>
                                        <SliderThumb/>
                                    </Slider>
                                </div>

                                <div className="flex flex-col gap-1">
                                    <label className="text-sm font-medium">Line Height: {lineHeightValue}</label>
                                    <Slider
                                        value={lineHeightValue}
                                        defaultValue={lineHeightValue}
                                        onChange={(value) => handleSliderChange(value as number, '', 'lineHeight', 'lineHeight')}
                                        onChangeEnd={() => handleInputBlur()}
                                        minValue={0.5}
                                        maxValue={3}
                                        step={0.1}
                                        aria-label="Line Height"
                                    >
                                        <SliderTrack>
                                            <SliderFillTrack/>
                                        </SliderTrack>
                                        <SliderThumb/>
                                    </Slider>
                                </div>

                                <HexColorPicker
                                    label="Text Color"
                                    aria-label="Text Color"
                                    value={color}
                                    onChange={(v) => {
                                        debouncedColorChange('color', v)
                                    }}
                                    onBlur={handleInputBlur}
                                />

                                <JollySelect
                                    label="Font Style"
                                    aria-label="Font Style"
                                    items={fontStyleOptions}
                                    selectedKey={fontStyle}
                                    onSelectionChange={(key) => handleSelectChange(key, 'fontStyle', 'fontStyle')}
                                >
                                    {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                                </JollySelect>

                                <JollySelect
                                    label="Text Align"
                                    aria-label="Text Align"
                                    items={textAlignOptions}
                                    selectedKey={textAlign}
                                    onSelectionChange={(key) => handleSelectChange(key, 'textAlign', 'textAlign')}
                                >
                                    {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                                </JollySelect>
                            </div>
                        </DisclosurePanel>
                    </Disclosure>

                    {/* Appearance */}
                    <Disclosure className="px-2 border-0 border-b border-b-secondary">
                        <DisclosureHeader className="font-semibold">Appearance</DisclosureHeader>
                        <DisclosurePanel className="pl-3">
                            <div className="flex flex-col gap-3">
                                <HexColorPicker
                                    label="Background Color"
                                    aria-label="Background Color"
                                    value={backgroundColor}
                                    onChange={(v) => {
                                        debouncedColorChange('backgroundColor', v)
                                    }}
                                    onBlur={handleInputBlur}
                                />

                                <div className="flex flex-col gap-1">
                                    <label className="text-sm font-medium">Border Radius: {borderRadiusValue}px</label>
                                    <Slider<number>
                                        value={borderRadiusValue}
                                        defaultValue={borderRadiusValue}
                                        onChange={(value) => handleSliderChange(value, 'px', 'borderRadius', 'borderRadius')}
                                        onChangeEnd={() => handleInputBlur()}
                                        minValue={0}
                                        maxValue={50}
                                        step={1}
                                        aria-label="Border Radius"
                                    >
                                        <SliderTrack>
                                            <SliderFillTrack/>
                                        </SliderTrack>
                                        <SliderThumb/>
                                    </Slider>
                                </div>

                                <div className="flex flex-col gap-1">
                                    <label className="text-sm font-medium">Border Width: {borderWidthValue}px</label>
                                    <Slider<number>
                                        value={borderWidthValue}
                                        defaultValue={borderWidthValue}
                                        onChange={(value) => {
                                            handleSliderChange(value, 'px', 'borderWidth', 'borderWidth')
                                            // Also set border style to solid if width > 0 and style is none
                                            if (value > 0 && selectedElement && (borderStyle === 'none' || !borderStyle)) {
                                                selectedElement.style.borderStyle = 'solid'
                                                styleValuesRef.current.borderStyle = 'solid'
                                                setRerender(prev => prev + 1) // Force re-render to update select
                                            }
                                        }}
                                        onChangeEnd={() => handleInputBlur()}
                                        minValue={0}
                                        maxValue={20}
                                        step={1}
                                        aria-label="Border Width"
                                        isDisabled={borderStyle === 'none'}
                                    >
                                        <SliderTrack>
                                            <SliderFillTrack/>
                                        </SliderTrack>
                                        <SliderThumb/>
                                    </Slider>
                                </div>

                                <JollySelect
                                    label="Border Style"
                                    aria-label="Border Style"
                                    items={borderStyleOptions}
                                    selectedKey={borderStyle}
                                    onSelectionChange={(key) => {
                                        handleSelectChange(key as Key, 'borderStyle', 'borderStyle')
                                        if (selectedElement) {
                                            // If style is set to none, set width to 0
                                            if (key === 'none') {
                                                handleSliderChange(0, 'px', 'borderWidth', 'borderWidth')
                                            }
                                            // If style is changed from none to something else, set a default width if it's 0
                                            else if (borderStyle === 'none' && borderWidthValue === 0) {
                                                handleSliderChange(1, 'px', 'borderWidth', 'borderWidth')
                                            }
                                        }
                                    }}
                                >
                                    {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                                </JollySelect>

                                <HexColorPicker
                                    label="Border Color"
                                    aria-label="Border Color"
                                    value={borderColor}
                                    onChange={(v) => {
                                        debouncedColorChange('borderColor', v)
                                    }}
                                    onBlur={handleInputBlur}
                                    disabled={borderStyle === 'none'}
                                />
                            </div>
                        </DisclosurePanel>
                    </Disclosure>

                    {/* Effects */}
                    <Disclosure className="px-2 border-0 border-b border-b-secondary">
                        <DisclosureHeader className="font-semibold">Effects</DisclosureHeader>
                        <DisclosurePanel className="pl-3">
                            <div className="flex flex-col gap-3">
                                <div>
                                    <h5 className="text-sm font-medium mb-2">Box Shadow</h5>
                                    <div className="flex flex-col gap-3">
                                        <JollySelect
                                            label="Shadow Position"
                                            aria-label="Shadow Position"
                                            items={boxShadowPositionOptions}
                                            selectedKey={boxShadowPosition || 'outset'}
                                            onSelectionChange={(key) => handleBoxShadowChange('position', key as string)}
                                        >
                                            {(item) => <SelectItem id={item.id}>{item.name}</SelectItem>}
                                        </JollySelect>

                                        <div className="grid grid-cols-2 gap-2">
                                            <div className="flex flex-col gap-1">
                                                <label className="text-sm font-medium">Offset X: {boxShadowOffsetX}px</label>
                                                <Slider<number>
                                                    value={boxShadowOffsetX}
                                                    defaultValue={boxShadowOffsetX}
                                                    onChange={(value) => handleBoxShadowChange('offsetX', value)}
                                                    minValue={-50}
                                                    maxValue={50}
                                                    step={1}
                                                    aria-label="Shadow Offset X"
                                                >
                                                    <SliderTrack>
                                                        <SliderFillTrack/>
                                                    </SliderTrack>
                                                    <SliderThumb/>
                                                </Slider>
                                            </div>

                                            <div className="flex flex-col gap-1">
                                                <label className="text-sm font-medium">Offset Y: {boxShadowOffsetY}px</label>
                                                <Slider<number>
                                                    value={boxShadowOffsetY}
                                                    defaultValue={boxShadowOffsetY}
                                                    onChange={(value) => handleBoxShadowChange('offsetY', value)}
                                                    minValue={-50}
                                                    maxValue={50}
                                                    step={1}
                                                    aria-label="Shadow Offset Y"
                                                >
                                                    <SliderTrack>
                                                        <SliderFillTrack/>
                                                    </SliderTrack>
                                                    <SliderThumb/>
                                                </Slider>
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-2 gap-2">
                                            <div className="flex flex-col gap-1">
                                                <label className="text-sm font-medium">Blur: {boxShadowBlur}px</label>
                                                <Slider<number>
                                                    value={boxShadowBlur}
                                                    defaultValue={boxShadowBlur}
                                                    onChange={(value) => handleBoxShadowChange('blur', value)}
                                                    minValue={0}
                                                    maxValue={50}
                                                    step={1}
                                                    aria-label="Shadow Blur"
                                                >
                                                    <SliderTrack>
                                                        <SliderFillTrack/>
                                                    </SliderTrack>
                                                    <SliderThumb/>
                                                </Slider>
                                            </div>

                                            <div className="flex flex-col gap-1">
                                                <label className="text-sm font-medium">Spread: {boxShadowSpread}px</label>
                                                <Slider<number>
                                                    value={boxShadowSpread}
                                                    defaultValue={boxShadowSpread}
                                                    onChange={(value) => handleBoxShadowChange('spread', value)}
                                                    minValue={-20}
                                                    maxValue={20}
                                                    step={1}
                                                    aria-label="Shadow Spread"
                                                >
                                                    <SliderTrack>
                                                        <SliderFillTrack/>
                                                    </SliderTrack>
                                                    <SliderThumb/>
                                                </Slider>
                                            </div>
                                        </div>

                                        <HexColorPicker
                                            label="Shadow Color"
                                            aria-label="Shadow Color"
                                            value={boxShadowColor}
                                            onChange={(v) => {
                                                debouncedColorChange('boxShadowColor', v)
                                            }}
                                            onBlur={handleInputBlur}
                                        />
                                    </div>
                                </div>
                            </div>
                        </DisclosurePanel>
                    </Disclosure>

                </div>
            ) : (
                <p className="px-2">Click on an element in the preview to view its details</p>
            )}
        </div>
    )
}

export default PropertiesPanel
