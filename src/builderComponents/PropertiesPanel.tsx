import { useCallback, useEffect, useRef, useState } from 'react'
import { Ta<PERSON>, Tab<PERSON><PERSON>, Tab, TabPanel } from '@/components/ui/tabs'
import StylesTab from './StylesTab'
import PropertiesTab from './PropertiesTab'
import {
    BoxShadowPositionValue,
    getDimensionStyle,
    getOriginalStyleValue,
    getStyleValue,
    parseBoxShadow
} from '@/lib/builder/styleUtils'

interface PropertiesPanelProps {
    selectedElement?: HTMLElement;
    onStyleChange: () => void;
    onAttributeChange?: (attribute: string, value: string) => void;
    onDomChange?: () => void;
}

type SpacingValues = {
    top: string;
    right: string;
    bottom: string;
    left: string;
};

type DisplayValue = 'block' | 'flex' | 'grid' | '';
type FlexDirectionValue = 'row' | 'column' | '';
type JustifyContentValue =
    'flex-start'
    | 'flex-end'
    | 'center'
    | 'space-between'
    | 'space-around'
    | 'space-evenly'
    | '';
type AlignItemsValue = 'start' | 'end' | 'center' | 'stretch' | '';
type FontWeightValue = 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900' | '';
type FontStyleValue = 'normal' | 'italic' | '';
type BorderStyleValue = 'none' | 'solid' | 'dashed' | 'dotted' | 'double' | '';
type PositionValue = 'static' | 'relative' | 'absolute' | 'fixed' | 'sticky' | '';
type TextAlignValue = 'left' | 'center' | 'right' | 'justify' | '';

const PropertiesPanel = ({selectedElement, onStyleChange, onAttributeChange, onDomChange}: PropertiesPanelProps) => {
    // Use refs to track current values without triggering re-renders
    const styleValuesRef = useRef({
        margin: {top: '', right: '', bottom: '', left: ''},
        padding: {top: '', right: '', bottom: '', left: ''},
        display: '' as DisplayValue,
        gridTemplateColumns: '',
        gridTemplateRows: '',
        flexDirection: '' as FlexDirectionValue,
        justifyContent: '' as JustifyContentValue,
        alignItems: '' as AlignItemsValue,
        gap: '',
        gapValue: 0,
        width: '',
        height: '',
        maxWidth: '',
        maxHeight: '',
        fontWeight: '' as FontWeightValue,
        fontSize: '',
        fontSizeValue: 16,
        lineHeight: '',
        lineHeightValue: 1.5,
        backgroundColor: '',
        borderRadius: '',
        borderRadiusValue: 0,
        borderWidth: '',
        borderWidthValue: 0,
        borderColor: '',
        borderStyle: '' as BorderStyleValue,
        color: '',
        fontStyle: '' as FontStyleValue,
        position: '' as PositionValue,
        top: '',
        right: '',
        bottom: '',
        left: '',
        textAlign: '' as TextAlignValue,
        boxShadow: '',
        boxShadowOffsetX: 0,
        boxShadowOffsetY: 0,
        boxShadowBlur: 0,
        boxShadowSpread: 0,
        boxShadowColor: '',
        boxShadowPosition: '' as BoxShadowPositionValue
    })

    // Use state for display-only purposes and active input tracking
    const [displayMode, setDisplayMode] = useState<DisplayValue>('')
    const [rerender, setRerender] = useState(0) // Use to force re-render when needed

    // Add controlled state for active input fields
    const [activeInputs, setActiveInputs] = useState({
        gridTemplateColumns: '',
        gridTemplateRows: '',
        gap: '',
        width: '',
        height: '',
        maxWidth: '',
        maxHeight: '',
        margin: {top: '', right: '', bottom: '', left: ''},
        padding: {top: '', right: '', bottom: '', left: ''},
        top: '',
        right: '',
        bottom: '',
        left: ''
    })

    // Add state for HTML attributes
    const [htmlAttributes, setHtmlAttributes] = useState({
        id: ''
    })

    // Debounced color change handler to improve performance
    const debouncedColorChange = useCallback((property: 'backgroundColor' | 'color' | 'borderColor' | 'boxShadowColor', value: string) => {
        if (!selectedElement) return

        if (property === 'boxShadowColor') {
            // Handle box shadow color specially - need to update the complete box-shadow property
            const values = styleValuesRef.current
            const shadowValue = `${values.boxShadowPosition === 'inset' ? 'inset ' : ''}${values.boxShadowOffsetX}px ${values.boxShadowOffsetY}px ${values.boxShadowBlur}px ${values.boxShadowSpread}px ${value}`
            selectedElement.style.boxShadow = shadowValue
            values.boxShadow = shadowValue
            values.boxShadowColor = value
        } else {
            (selectedElement.style as any)[property] = value;
            (styleValuesRef.current as any)[property] = value
        }
        onStyleChange()
    }, [onStyleChange, selectedElement])

    // Load styles from element when it changes
    useEffect(() => {
        if (selectedElement) {
            const styles = window.getComputedStyle(selectedElement)
            const values = styleValuesRef.current

            // Spacing values - try to get original values first
            values.margin = {
                top: getStyleValue(selectedElement, 'marginTop', styles),
                right: getStyleValue(selectedElement, 'marginRight', styles),
                bottom: getStyleValue(selectedElement, 'marginBottom', styles),
                left: getStyleValue(selectedElement, 'marginLeft', styles),
            }

            values.padding = {
                top: getStyleValue(selectedElement, 'paddingTop', styles),
                right: getStyleValue(selectedElement, 'paddingRight', styles),
                bottom: getStyleValue(selectedElement, 'paddingBottom', styles),
                left: getStyleValue(selectedElement, 'paddingLeft', styles),
            }

            // Layout values
            const originalDisplay = getOriginalStyleValue(selectedElement, 'display')
            const currentDisplay = (originalDisplay || styles.display) as DisplayValue
            values.display = currentDisplay === 'flex' || currentDisplay === 'grid' ? currentDisplay : 'block'
            setDisplayMode(values.display) // Need to update state for conditional rendering

            values.gridTemplateColumns = getStyleValue(selectedElement, 'gridTemplateColumns', styles)
            values.gridTemplateRows = getStyleValue(selectedElement, 'gridTemplateRows', styles)
            values.flexDirection = (getStyleValue(selectedElement, 'flexDirection', styles) as FlexDirectionValue) || 'row'
            values.justifyContent = (getStyleValue(selectedElement, 'justifyContent', styles) as JustifyContentValue) || 'flex-start'
            values.alignItems = (getStyleValue(selectedElement, 'alignItems', styles) as AlignItemsValue) || 'stretch'
            values.gap = getStyleValue(selectedElement, 'gap', styles)
            values.gapValue = parseInt(styles.gap) || 0

            // Position values
            values.position = (getStyleValue(selectedElement, 'position', styles) as PositionValue) || 'static'
            values.top = getStyleValue(selectedElement, 'top', styles)
            values.right = getStyleValue(selectedElement, 'right', styles)
            values.bottom = getStyleValue(selectedElement, 'bottom', styles)
            values.left = getStyleValue(selectedElement, 'left', styles)

            // Typography values
            values.fontWeight = (getStyleValue(selectedElement, 'fontWeight', styles) as FontWeightValue) || 'normal'
            values.fontSize = getStyleValue(selectedElement, 'fontSize', styles)
            values.fontSizeValue = parseInt(styles.fontSize) || 16
            values.lineHeight = getStyleValue(selectedElement, 'lineHeight', styles)
            values.lineHeightValue = parseFloat(styles.lineHeight) || 1.5
            values.color = styles.color
            values.fontStyle = (getStyleValue(selectedElement, 'fontStyle', styles) as FontStyleValue) || 'normal'
            values.textAlign = (getStyleValue(selectedElement, 'textAlign', styles) as TextAlignValue) || ''

            // Dimension values - use special function to detect percentages
            values.width = getDimensionStyle(selectedElement, 'width')
            values.height = getDimensionStyle(selectedElement, 'height')
            values.maxWidth = getDimensionStyle(selectedElement, 'maxWidth')
            values.maxHeight = getDimensionStyle(selectedElement, 'maxHeight')

            // Appearance values
            values.backgroundColor = styles.backgroundColor
            values.borderRadius = getStyleValue(selectedElement, 'borderRadius', styles)
            values.borderRadiusValue = parseInt(styles.borderRadius) || 0
            values.borderWidth = styles.borderWidth //getStyleValue(selectedElement, 'borderWidth', styles)
            values.borderWidthValue = parseInt(values.borderWidth) || 0
            values.borderColor = styles.borderColor
            values.borderStyle = (getStyleValue(selectedElement, 'borderStyle', styles) as BorderStyleValue) || 'none'

            // Box shadow values
            const boxShadowValue = getStyleValue(selectedElement, 'boxShadow', styles)
            const parsedShadow = parseBoxShadow(boxShadowValue)
            values.boxShadow = boxShadowValue
            values.boxShadowOffsetX = parsedShadow.offsetX
            values.boxShadowOffsetY = parsedShadow.offsetY
            values.boxShadowBlur = parsedShadow.blur
            values.boxShadowSpread = parsedShadow.spread
            values.boxShadowColor = parsedShadow.color
            values.boxShadowPosition = parsedShadow.position

            // Update activeInputs state for controlled inputs
            setActiveInputs({
                gridTemplateColumns: values.gridTemplateColumns,
                gridTemplateRows: values.gridTemplateRows,
                gap: values.gap,
                width: values.width,
                height: values.height,
                maxWidth: values.maxWidth,
                maxHeight: values.maxHeight,
                margin: {...values.margin},
                padding: {...values.padding},
                top: values.top,
                right: values.right,
                bottom: values.bottom,
                left: values.left
            })

            // Load HTML attributes
            setHtmlAttributes({
                id: selectedElement.id || ''
            })

            // Force a re-render to update the UI with new values
            setRerender(prev => prev + 1)
        } else {
            // Reset values when no element is selected
            const values = styleValuesRef.current
            values.margin = {top: '', right: '', bottom: '', left: ''}
            values.padding = {top: '', right: '', bottom: '', left: ''}
            values.display = ''
            setDisplayMode('')
            values.gridTemplateColumns = ''
            values.gridTemplateRows = ''
            values.flexDirection = ''
            values.justifyContent = ''
            values.alignItems = ''
            values.gap = ''
            values.gapValue = 0
            values.fontWeight = ''
            values.fontSize = ''
            values.fontSizeValue = 16
            values.lineHeight = ''
            values.lineHeightValue = 1.5
            values.backgroundColor = ''
            values.borderRadius = ''
            values.borderRadiusValue = 0
            values.borderWidth = ''
            values.borderWidthValue = 0
            values.borderColor = ''
            values.borderStyle = ''
            values.color = ''
            values.width = ''
            values.height = ''
            values.maxWidth = ''
            values.maxHeight = ''
            values.fontStyle = ''
            values.position = ''
            values.top = ''
            values.right = ''
            values.bottom = ''
            values.left = ''
            values.textAlign = ''
            values.boxShadow = ''
            values.boxShadowOffsetX = 0
            values.boxShadowOffsetY = 0
            values.boxShadowBlur = 0
            values.boxShadowSpread = 0
            values.boxShadowColor = ''
            values.boxShadowPosition = ''

            // Reset activeInputs state
            setActiveInputs({
                gridTemplateColumns: '',
                gridTemplateRows: '',
                gap: '',
                width: '',
                height: '',
                maxWidth: '',
                maxHeight: '',
                margin: {top: '', right: '', bottom: '', left: ''},
                padding: {top: '', right: '', bottom: '', left: ''},
                top: '',
                right: '',
                bottom: '',
                left: ''
            })

            // Reset HTML attributes
            setHtmlAttributes({
                id: ''
            })

            // Force a re-render to update the UI
            setRerender(prev => prev + 1)
        }
    }, [selectedElement])

    // Update style without changing state
    const updateStyle = useCallback((property: keyof CSSStyleDeclaration, value: string) => {
        if (!selectedElement) return;
        (selectedElement.style as any)[property] = value;

        // Update ref value but don't trigger a re-render
        (styleValuesRef.current as any)[property] = value
        onStyleChange() // Only save to file when an input loses focus
    }, [onStyleChange, selectedElement])

    // Handle blur - only save changes on blur
    const handleInputBlur = useCallback(() => {
        // if (selectedElement) {
        //   onStyleChange(); // Only save to file when an input loses focus
        // }
    }, [selectedElement, onStyleChange])

    // Handle margin/padding changes
    const handleSpacingChange = useCallback((
        type: 'margin' | 'padding',
        direction: keyof SpacingValues,
        value: string
    ) => {
        if (!selectedElement) return
        const sanitizedValue = value.match(/^[0-9.]+(px|em|rem|%)?$/) ? value : ''
        const cssProperty = `${type}${direction.charAt(0).toUpperCase() + direction.slice(1)}` as keyof CSSStyleDeclaration

        // Update the DOM style directly
        updateStyle(cssProperty, sanitizedValue)

        // Update the ref without triggering re-render
        styleValuesRef.current[type][direction] = sanitizedValue

        // Update the controlled state for this specific input
        setActiveInputs(prev => ({
            ...prev,
            [type]: {
                ...prev[type],
                [direction]: value // Use the original input value for controlled input
            }
        }))
    }, [selectedElement, updateStyle])

    // Handle position values changes
    const handlePositionChange = useCallback((
        property: 'top' | 'right' | 'bottom' | 'left',
        value: string
    ) => {
        if (!selectedElement) return
        const sanitizedValue = value.match(/^[0-9.-]+(px|em|rem|%)?$/) ? value : ''

        // Update the DOM style directly
        updateStyle(property, sanitizedValue)

        // Update the ref without triggering re-render
        styleValuesRef.current[property] = sanitizedValue

        // Update the controlled state for this specific input
        setActiveInputs(prev => ({
            ...prev,
            [property]: value // Use the original input value for controlled input
        }))
    }, [selectedElement, updateStyle])

    // Handle box shadow changes
    const handleBoxShadowChange = useCallback((
        property: 'offsetX' | 'offsetY' | 'blur' | 'spread' | 'position',
        value: number | string
    ) => {
        if (!selectedElement) return

        const values = styleValuesRef.current

        // Update the specific property
        switch (property) {
            case 'offsetX':
                values.boxShadowOffsetX = value as number
                break
            case 'offsetY':
                values.boxShadowOffsetY = value as number
                break
            case 'blur':
                values.boxShadowBlur = value as number
                break
            case 'spread':
                values.boxShadowSpread = value as number
                break
            case 'position':
                values.boxShadowPosition = value as BoxShadowPositionValue
                break
        }

        // Construct the complete box-shadow value
        const shadowValue = values.boxShadowOffsetX === 0 && values.boxShadowOffsetY === 0 && values.boxShadowBlur === 0 && values.boxShadowSpread === 0
            ? 'none'
            : `${values.boxShadowPosition === 'inset' ? 'inset ' : ''}${values.boxShadowOffsetX}px ${values.boxShadowOffsetY}px ${values.boxShadowBlur}px ${values.boxShadowSpread}px ${values.boxShadowColor || 'rgba(0, 0, 0, 0.1)'}`

        // Update the DOM and ref
        selectedElement.style.boxShadow = shadowValue
        values.boxShadow = shadowValue

        // Force re-render for slider UI
        setRerender(prev => prev + 1)

        // Save changes
        onStyleChange()
    }, [selectedElement, onStyleChange])

    // Handle select changes
    const handleSelectChange = useCallback((
        key: React.Key,
        property: keyof typeof styleValuesRef.current,
        styleProperty: keyof CSSStyleDeclaration
    ) => {
        const newValue = key as string

        // Update the DOM style directly
        updateStyle(styleProperty, newValue);

        // Update the ref
        (styleValuesRef.current as any)[property] = newValue

        // Special case for display to update conditional rendering
        if (property === 'display') {
            setDisplayMode(newValue as DisplayValue)
        }

        // Select changes are discrete actions, save right away
        onStyleChange()
    }, [updateStyle, onStyleChange])

    // Handle slider changes
    const handleSliderChange = useCallback((
        value: number,
        unit: string,
        valueProperty: keyof typeof styleValuesRef.current,
        styleProperty: keyof CSSStyleDeclaration
    ) => {
        // Create the string value (e.g., "16px")
        const stringValue = `${value}${unit}`

        // Update the DOM style directly
        updateStyle(styleProperty, stringValue);

        // Update the refs
        (styleValuesRef.current as any)[valueProperty + 'Value'] = value;
        (styleValuesRef.current as any)[valueProperty] = stringValue

        // Special handling for gap to also update activeInputs
        if (valueProperty === 'gap') {
            setActiveInputs(prev => ({...prev, gap: stringValue}))
        }

        // Force re-render for slider UI
        setRerender(prev => prev + 1)
    }, [updateStyle])

    // Handle HTML attribute changes
    const handleAttributeChange = useCallback((attribute: string, value: string) => {
        if (!selectedElement) return

        // Update the DOM attribute
        if (value.trim() === '') {
            selectedElement.removeAttribute(attribute)
        } else {
            selectedElement.setAttribute(attribute, value)
        }

        // Update local state
        setHtmlAttributes(prev => ({
            ...prev,
            [attribute]: value
        }))

        // Call the parent callback to save changes
        onAttributeChange?.(attribute, value)
    }, [selectedElement, onAttributeChange])

    return (
        <div className="w-full h-full py-2 overflow-y-auto flex flex-col gap-4 relative">
            <div className="flex flex-col gap-2 px-2">
                <h3 className="text-lg font-semibold">Properties</h3>
                {selectedElement && (
                    <p className="text-sm text-muted-foreground">
                        <strong>Tag:</strong> {selectedElement.tagName.toLowerCase()}
                    </p>
                )}
            </div>

            {selectedElement ? (
                <Tabs defaultSelectedKey="styles" className="px-2">
                    <TabList>
                        <Tab id="styles">Styles</Tab>
                        <Tab id="properties">Properties</Tab>
                    </TabList>

                    <TabPanel id="styles">
                        <StylesTab
                            selectedElement={selectedElement}
                            styleValues={styleValuesRef.current}
                            activeInputs={activeInputs}
                            displayMode={displayMode}
                            updateStyle={updateStyle}
                            handleInputBlur={handleInputBlur}
                            handleSpacingChange={handleSpacingChange}
                            handlePositionChange={handlePositionChange}
                            handleBoxShadowChange={handleBoxShadowChange}
                            handleSelectChange={handleSelectChange}
                            handleSliderChange={handleSliderChange}
                            debouncedColorChange={debouncedColorChange}
                            setActiveInputs={setActiveInputs}
                            styleValuesRef={styleValuesRef}
                            setRerender={setRerender}
                        />
                    </TabPanel>

                    <TabPanel id="properties">
                        <PropertiesTab
                            selectedElement={selectedElement}
                            htmlAttributes={htmlAttributes}
                            handleAttributeChange={handleAttributeChange}
                            onDomChange={onDomChange}
                        />
                    </TabPanel>
                </Tabs>
            ) : (
                <p className="px-2">Click on an element in the preview to view its details</p>
            )}
        </div>
    )
}

export default PropertiesPanel
