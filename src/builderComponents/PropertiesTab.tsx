import { JollyTextField } from '@/components/ui/textfield'

interface PropertiesTabProps {
    selectedElement?: HTMLElement;
    htmlAttributes: {
        id: string;
    };
    handleAttributeChange: (attribute: string, value: string) => void;
}

const PropertiesTab = ({
    selectedElement,
    htmlAttributes,
    handleAttributeChange
}: PropertiesTabProps) => {
    return (
        <div className="mt-4 flex flex-col gap-3">
            <JollyTextField
                label="ID"
                aria-label="Element ID"
                value={htmlAttributes.id}
                onChange={(value) => handleAttributeChange('id', value)}
                placeholder="Enter element ID"
            />
        </div>
    )
}

export default PropertiesTab
